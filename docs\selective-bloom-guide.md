# 分层辉光功能使用指南

## 概述

分层辉光（Selective Bloom）功能已成功集成到Earth3D项目中，基于Three.js官方示例`webgl_postprocessing_unreal_bloom_selective`实现。该功能允许选择性地为特定对象添加辉光效果，而不影响场景中的其他对象。

## 功能特性

### 1. 独立的辉光系统
- **全局辉光**：影响整个场景中亮度超过阈值的所有像素
- **分层辉光**：只影响被明确添加到辉光层的对象

### 2. 独立的GUI控制
- **全局辉光控制面板**：调整全局辉光的阈值、强度、半径等参数
- **分层辉光控制面板**：独立调整分层辉光的参数

### 3. 自动集成
- OrbitingStars组件已自动添加到分层辉光层
- 默认启用分层辉光模式以展示效果

## 技术实现

### 核心组件

#### 1. SelectiveBloomManager
```javascript
// 位置: src/components/three/models/Scene/SelectiveBloomManager.js
// 功能: 管理分层辉光的渲染流程
```

主要方法：
- `addToBloomLayer(object)` - 添加对象到辉光层
- `removeFromBloomLayer(object)` - 从辉光层移除对象
- `toggleBloom(object)` - 切换对象的辉光状态
- `render()` - 渲染分层辉光效果

#### 2. PostProcessingManager (已更新)
```javascript
// 位置: src/components/three/models/Scene/PostProcessingManager.js
// 功能: 统一管理全局辉光和分层辉光
```

新增方法：
- `setSelectiveBloomParams(params)` - 设置分层辉光参数
- `getSelectiveBloomParams()` - 获取分层辉光参数
- `addToBloomLayer(object)` - 添加对象到辉光层
- `removeFromBloomLayer(object)` - 从辉光层移除对象
- `toggleBloom(object)` - 切换对象辉光状态

### 渲染流程

1. **辉光通道渲染**：
   - 设置相机只渲染辉光层（Layer 1）
   - 将非辉光对象材质替换为黑色
   - 渲染到辉光合成器
   - 应用UnrealBloomPass

2. **最终合成**：
   - 渲染正常场景
   - 使用自定义着色器混合辉光效果
   - 输出最终结果

## 使用方法

### 1. 通过GUI控制

在浏览器中打开项目后，可以在GUI面板中找到：
- **全局辉光 (Global Bloom)** - 控制传统的全局辉光效果
- **分层辉光 (Selective Bloom)** - 控制选择性辉光效果

### 2. 通过代码控制

```javascript
// 获取后处理管理器
const postProcessingManager = scene.postProcessingManager;

// 添加对象到辉光层
postProcessingManager.addToBloomLayer(yourObject);

// 移除对象从辉光层
postProcessingManager.removeFromBloomLayer(yourObject);

// 切换对象辉光状态
postProcessingManager.toggleBloom(yourObject);

// 设置分层辉光参数
postProcessingManager.setSelectiveBloomParams({
  enabled: true,
  strength: 1.0,
  radius: 0.4,
  threshold: 0.0
});
```

### 3. 为新组件添加辉光

```javascript
// 在组件创建后
const myComponent = new MyComponent(scene);

// 添加到分层辉光
scene.postProcessingManager.addToBloomLayer(myComponent.mesh);
```

## 参数说明

### 分层辉光参数
- **enabled**: 是否启用分层辉光
- **threshold**: 辉光阈值（通常设为0.0，因为我们控制哪些对象发光）
- **strength**: 辉光强度（0.0-3.0）
- **radius**: 辉光半径（0.0-1.0）
- **exposure**: 曝光度（0.1-2.0）

### 全局辉光参数
- **enabled**: 是否启用全局辉光
- **threshold**: 亮度阈值（0.0-1.0，只有超过此亮度的像素才会发光）
- **strength**: 辉光强度（0.0-3.0）
- **radius**: 辉光半径（0.0-1.0）
- **exposure**: 曝光度（0.1-2.0）

## 性能考虑

1. **渲染开销**：分层辉光需要额外的渲染通道，会增加GPU负担
2. **内存使用**：需要额外的渲染目标和纹理
3. **优化建议**：
   - 合理控制辉光对象数量
   - 根据设备性能调整辉光质量
   - 在移动设备上考虑禁用或降低辉光效果

## 故障排除

### 常见问题

1. **辉光效果不显示**
   - 检查对象是否正确添加到辉光层
   - 确认分层辉光已启用
   - 检查辉光强度是否设置过低

2. **性能问题**
   - 降低辉光强度和半径
   - 减少辉光对象数量
   - 检查渲染目标分辨率

3. **GUI控制无效**
   - 确认GUI正确初始化
   - 检查参数绑定是否正确

## 扩展功能

未来可以考虑添加：
- 动态辉光强度动画
- 基于距离的辉光衰减
- 多层辉光效果
- 辉光颜色控制
- 预设辉光配置

## 相关文件

- `src/components/three/models/Scene/SelectiveBloomManager.js` - 分层辉光管理器
- `src/components/three/models/Scene/PostProcessingManager.js` - 后处理管理器
- `src/components/three/models/Scene/Scene.js` - 主场景文件
- `src/components/three/models/OrbitingStars/OrbitingStars.js` - 轨道星星组件
