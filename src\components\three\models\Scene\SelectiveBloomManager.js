import * as THREE from "three";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer.js";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass.js";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass.js";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass.js";
import { OutputPass } from "three/examples/jsm/postprocessing/OutputPass.js";

/**
 * SelectiveBloomManager - 分层辉光管理器
 * 基于Three.js官方示例webgl_postprocessing_unreal_bloom_selective实现
 * 允许选择性地为特定对象添加辉光效果
 */
class SelectiveBloomManager {
  constructor(scene, camera, renderer) {
    this.scene = scene;
    this.camera = camera;
    this.renderer = renderer;

    // 渲染器和合成器
    this.bloomComposer = null;
    this.finalComposer = null;

    // 渲染通道
    this.renderScene = null;
    this.bloomPass = null;
    this.outputPass = null;

    // 材质和层级管理
    this.bloomLayer = new THREE.Layers();
    this.bloomLayer.set(1); // 使用层级1作为辉光层
    this.darkMaterial = new THREE.MeshBasicMaterial({ color: "black" });
    this.materials = new Map(); // 存储原始材质

    // 辉光参数
    this.bloomParams = {
      enabled: true,
      threshold: 0.0, // 分层辉光通常设置为0，因为我们控制哪些对象发光
      strength: 1.5,
      radius: 0.4,
      exposure: 1.0,
    };

    // 混合着色器 - 用于合成最终结果
    this.mixPass = null;

    this.init();
  }

  init() {
    this.createComposers();
    this.createPasses();
    this.createMixShader();
  }

  /**
   * 创建效果合成器
   */
  createComposers() {
    // 辉光合成器 - 只渲染辉光对象
    this.bloomComposer = new EffectComposer(this.renderer);
    this.bloomComposer.renderTarget1.samples = 2;
    this.bloomComposer.renderTarget2.samples = 2;

    // 最终合成器 - 合成正常场景和辉光效果
    this.finalComposer = new EffectComposer(this.renderer);
    this.finalComposer.renderTarget1.samples = 2;
    this.finalComposer.renderTarget2.samples = 2;
  }

  /**
   * 创建渲染通道
   */
  createPasses() {
    // 1. 辉光场景渲染通道
    this.renderScene = new RenderPass(this.scene, this.camera);
    this.bloomComposer.addPass(this.renderScene);

    // 2. 辉光通道
    this.bloomPass = new UnrealBloomPass(new THREE.Vector2(window.innerWidth, window.innerHeight), this.bloomParams.strength, this.bloomParams.radius, this.bloomParams.threshold);
    this.bloomComposer.addPass(this.bloomPass);

    // 3. 最终场景渲染通道
    const finalPass = new RenderPass(this.scene, this.camera);
    this.finalComposer.addPass(finalPass);

    // 4. 输出通道
    this.outputPass = new OutputPass();
    this.finalComposer.addPass(this.outputPass);

    // 设置曝光度
    this.renderer.toneMappingExposure = this.bloomParams.exposure;
  }

  /**
   * 创建混合着色器
   */
  createMixShader() {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `;

    const fragmentShader = `
      uniform sampler2D baseTexture;
      uniform sampler2D bloomTexture;
      varying vec2 vUv;
      
      void main() {
        vec4 base = texture2D(baseTexture, vUv);
        vec4 bloom = texture2D(bloomTexture, vUv);
        
        // 使用加法混合模式合成辉光效果
        gl_FragColor = base + bloom;
      }
    `;

    this.mixPass = new ShaderPass(
      new THREE.ShaderMaterial({
        uniforms: {
          baseTexture: { value: null },
          bloomTexture: { value: null },
        },
        vertexShader,
        fragmentShader,
        defines: {},
      }),
      "baseTexture"
    );

    this.mixPass.needsSwap = true;
    this.finalComposer.addPass(this.mixPass);
  }

  /**
   * 添加对象到辉光层
   */
  addToBloomLayer(object) {
    if (object.isMesh || object.isPoints || object.isLine) {
      object.layers.enable(1); // 启用辉光层
    }

    // 递归处理子对象
    object.traverse((child) => {
      if (child.isMesh || child.isPoints || child.isLine) {
        child.layers.enable(1);
      }
    });
  }

  /**
   * 从辉光层移除对象
   */
  removeFromBloomLayer(object) {
    if (object.isMesh || object.isPoints || object.isLine) {
      object.layers.disable(1); // 禁用辉光层
    }

    // 递归处理子对象
    object.traverse((child) => {
      if (child.isMesh || child.isPoints || child.isLine) {
        child.layers.disable(1);
      }
    });
  }

  /**
   * 切换对象的辉光状态
   */
  toggleBloom(object) {
    if (this.isInBloomLayer(object)) {
      this.removeFromBloomLayer(object);
    } else {
      this.addToBloomLayer(object);
    }
  }

  /**
   * 检查对象是否在辉光层中
   */
  isInBloomLayer(object) {
    return object.layers.test(this.bloomLayer);
  }

  /**
   * 准备辉光渲染 - 隐藏非辉光对象
   */
  darkenNonBloomed() {
    this.scene.traverse((obj) => {
      if (obj.isMesh || obj.isPoints || obj.isLine) {
        if (!obj.layers.test(this.bloomLayer)) {
          // 保存原始材质
          if (obj.material) {
            this.materials.set(obj, obj.material);
            obj.material = this.darkMaterial;
          }
        }
      }
    });
  }

  /**
   * 恢复原始材质
   */
  restoreMaterials() {
    this.materials.forEach((originalMaterial, obj) => {
      obj.material = originalMaterial;
    });
    this.materials.clear();
  }

  /**
   * 渲染分层辉光效果
   */
  render() {
    if (!this.bloomParams.enabled) {
      // 如果辉光被禁用，直接渲染场景
      this.renderer.render(this.scene, this.camera);
      return;
    }

    // 1. 渲染辉光通道
    this.camera.layers.set(1); // 只渲染辉光层
    this.darkenNonBloomed(); // 将非辉光对象变黑
    this.bloomComposer.render();
    this.restoreMaterials(); // 恢复材质

    // 2. 渲染最终合成
    this.camera.layers.set(0); // 渲染所有层
    if (this.mixPass && this.mixPass.uniforms) {
      this.mixPass.uniforms.bloomTexture.value = this.bloomComposer.renderTarget2.texture;
    }
    this.finalComposer.render();
  }

  /**
   * 处理窗口大小变化
   */
  onWindowResize(width, height) {
    this.bloomComposer.setSize(width, height);
    this.finalComposer.setSize(width, height);

    // 更新辉光通道的分辨率
    this.bloomPass.setSize(width, height);
  }

  /**
   * 设置像素比
   */
  setPixelRatio(pixelRatio) {
    this.bloomComposer.setPixelRatio(pixelRatio);
    this.finalComposer.setPixelRatio(pixelRatio);
  }

  /**
   * 获取辉光参数
   */
  getBloomParams() {
    return { ...this.bloomParams };
  }

  /**
   * 设置辉光参数
   */
  setBloomParams(params) {
    Object.assign(this.bloomParams, params);

    if (this.bloomPass) {
      this.bloomPass.threshold = this.bloomParams.threshold;
      this.bloomPass.strength = this.bloomParams.strength;
      this.bloomPass.radius = this.bloomParams.radius;
    }

    if (this.renderer) {
      this.renderer.toneMappingExposure = this.bloomParams.exposure;
    }
  }

  /**
   * 销毁资源
   */
  destroy() {
    if (this.bloomComposer) {
      this.bloomComposer.dispose();
    }
    if (this.finalComposer) {
      this.finalComposer.dispose();
    }

    this.materials.clear();
    this.darkMaterial.dispose();
  }
}

export { SelectiveBloomManager };
